# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Walmart data processing toolkit that merges CSV files (Settlement and Storage reports) into consolidated formats. The project contains two main Python scripts that handle different merging scenarios.

## Core Scripts Architecture

### merge_walmart_csv.py
- **Purpose**: Merges two specific CSV files (Settlement and Storage) into a single CSV
- **Input**: Hardcoded filenames for Settlement and Storage CSV files
- **Output**: Single CSV file with all data concatenated, maintaining original formatting
- **Key Logic**: Pads rows to ensure consistent column count across merged data

### merge_weekly_walmart_excel.py
- **Purpose**: Bat<PERSON> processes all CSV files in a selected folder and creates an Excel workbook with three sheets
- **Input**: User-selected folder containing multiple CSV files
- **Output**: Excel file with three sheets:
  - Settlement: All Settlement-type CSV data
  - Storage: All Storage-type CSV data  
  - 总表 (Total): All CSV data combined
- **Key Features**:
  - GUI folder selection using tkinter
  - Progress tracking during processing
  - Auto-adjusts column widths
  - Shows completion dialog

## CSV File Structure

The CSV files follow a specific Walmart report format:
- **Settlement files**: Financial transaction data with 20 columns
- **Storage files**: Warehouse storage fee data with 18 columns
- Both types have 3-row headers containing company info and date ranges

## Common Development Commands

```bash
# Run the simple CSV merger
python merge_walmart_csv.py [output_filename]

# Run the Excel merger with GUI
python merge_weekly_walmart_excel.py

# Build executable with PyInstaller
pyinstaller --onefile --windowed --name "Walmart数据合并工具" merge_weekly_walmart_excel.py
```

## Dependencies

- Python 3.x
- openpyxl (for Excel operations)
- tkinter (included in Python, for GUI)
- PyInstaller (for building executables)

## File Encoding

All CSV files use UTF-8 with BOM encoding. The scripts handle this with `encoding='utf-8-sig'` to properly read Chinese characters and special symbols.

## Deployment

The project includes PyInstaller spec file for building standalone Windows executables. The built exe files are located in the `dist/` directory.