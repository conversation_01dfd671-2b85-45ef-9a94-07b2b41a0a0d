# Walmart数据合并工具 使用说明

## 工具简介

本工具用于合并Walmart的CSV数据文件，支持两种合并方式：
1. 简单CSV合并 - 将两个CSV文件合并成一个
2. 批量Excel合并 - 将文件夹内所有CSV文件合并到Excel的不同sheet页

## 使用方法

### 方法一：使用打包好的exe程序（推荐）

1. **运行程序**
   - 双击 `dist\Walmart数据合并工具.exe`

2. **选择文件夹**
   - 在弹出的对话框中选择包含CSV文件的文件夹
   - 例如：选择"一周"文件夹

3. **等待处理**
   - 程序会显示处理进度
   - 处理完成后会弹出提示框

4. **查看结果**
   - 生成的Excel文件会保存在选择文件夹的同级目录
   - 文件名格式：`[文件夹名]_数据汇总.xlsx`
   - 例如：选择"一周"文件夹，生成"一周_数据汇总.xlsx"

### 方法二：运行Python脚本

#### 批量Excel合并（merge_weekly_walmart_excel.py）

```bash
python merge_weekly_walmart_excel.py
```

功能与exe程序相同，需要安装Python环境和依赖包：
- openpyxl
- tkinter（Python自带）

#### 简单CSV合并（merge_walmart_csv.py）

```bash
# 默认输出到 @合并.csv
python merge_walmart_csv.py

# 指定输出文件名
python merge_walmart_csv.py "自定义文件名.csv"
```

注意：此脚本使用硬编码的文件名，需要修改代码中的文件名才能处理其他文件。

## 输出文件说明

### Excel文件包含三个sheet页：

1. **Settlement**
   - 包含所有Settlement（结算）类型的CSV数据
   - 按文件顺序排列

2. **Storage**  
   - 包含所有Storage（仓储费）类型的CSV数据
   - 按文件顺序排列

3. **总表**
   - 包含文件夹内所有CSV文件的完整数据
   - Settlement和Storage数据都在其中

### 数据格式保留

- 保留每个CSV文件的原始格式
- 包括前3行的公司信息和日期范围
- 自动处理不同列数的对齐问题
- 支持中文和特殊字符

## CSV文件命名规范

工具通过文件名中的关键字来识别文件类型：
- 包含"Settlement"的文件 → Settlement sheet
- 包含"Storage"的文件 → Storage sheet

文件名示例：
- `CoCluB-Walmart-2025-05-31-2025-06-06-Settlement.csv`
- `CoCluB-Walmart-2025-05-31-2025-06-06-Storage.csv`

## 常见问题

### Q: 程序无法运行？
A: 确保系统为Windows，如果是exe文件可能被杀毒软件拦截，请添加信任。

### Q: 找不到CSV文件？
A: 确保选择的文件夹内有.csv格式的文件，文件名需要包含Settlement或Storage关键字。

### Q: Excel文件打不开？
A: 确保电脑安装了Excel或WPS等表格软件，文件格式为标准的.xlsx格式。

### Q: 中文显示乱码？
A: 本工具已处理编码问题，如仍有乱码，请确认原始CSV文件的编码格式。

## 注意事项

1. 处理大量文件时可能需要较长时间，请耐心等待
2. 确保有足够的磁盘空间存储生成的Excel文件
3. 不要在处理过程中关闭程序窗口
4. 生成的文件会自动保存，无需手动保存

## 技术支持

如遇到问题，请检查：
1. CSV文件格式是否正确
2. 文件名是否包含必要的关键字
3. 是否有文件被其他程序占用

---

版本：1.0  
更新日期：2025年