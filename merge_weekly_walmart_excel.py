import csv
import os
import sys
from datetime import datetime
import openpyxl
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, PatternFill, Alignment
import tkinter as tk
from tkinter import filedialog, messagebox

def read_csv_file(filepath):
    """读取单个CSV文件，返回所有行"""
    try:
        with open(filepath, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.reader(f)
            return list(reader)
    except Exception as e:
        print(f"读取文件 {filepath} 时出错: {e}")
        return []

def get_max_columns(all_data):
    """获取所有数据中的最大列数"""
    max_cols = 0
    for file_data in all_data:
        for row in file_data['rows']:
            max_cols = max(max_cols, len(row))
    return max_cols

def pad_row(row, target_length):
    """用空字符串填充行到目标长度"""
    return row + [''] * (target_length - len(row))

def write_to_sheet(ws, data_list, sheet_name):
    """将数据写入Excel工作表"""
    print(f"\n正在写入 {sheet_name} sheet...")
    
    row_num = 1
    total_data_rows = 0
    
    # 获取最大列数
    max_cols = get_max_columns(data_list)
    
    for idx, file_data in enumerate(data_list):
        filename = file_data['filename']
        rows = file_data['rows']
        
        # if idx > 0:
        #     # 在不同文件之间添加空行
        #     row_num += 1
        
        # 写入文件名作为标识（可选）
        # ws.cell(row=row_num, column=1, value=f"=== {filename} ===")
        # row_num += 1
        
        # 写入所有行
        for row in rows:
            padded_row = pad_row(row, max_cols)
            for col_idx, value in enumerate(padded_row, 1):
                ws.cell(row=row_num, column=col_idx, value=value)
            row_num += 1
            
            # 统计数据行（跳过前3行头部信息和空行）
            if len(row) > 0 and row[0] and row_num > 4:
                total_data_rows += 1
    
    # 自动调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        
        for cell in column[:100]:  # 只检查前100行以提高性能
            try:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            except:
                pass
        
        adjusted_width = min(max_length + 2, 50)  # 最大宽度限制为50
        ws.column_dimensions[column_letter].width = adjusted_width
    
    print(f"{sheet_name} sheet 写入完成，共 {row_num-1} 行")
    return total_data_rows

def select_folder():
    """弹出文件夹选择对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    folder_path = filedialog.askdirectory(
        title="请选择包含CSV文件的文件夹",
        initialdir=os.getcwd()
    )
    
    root.destroy()
    return folder_path

def merge_weekly_walmart_to_excel():
    """合并选择的文件夹中的所有Walmart CSV文件到Excel"""
    
    # 选择文件夹
    weekly_folder = select_folder()
    
    if not weekly_folder:
        print("未选择文件夹，程序退出")
        return
    
    # 获取文件夹名称
    folder_name = os.path.basename(weekly_folder)
    
    # 设置输出文件路径（在选择的文件夹同级目录）
    parent_dir = os.path.dirname(weekly_folder)
    output_file = os.path.join(parent_dir, f"{folder_name}_数据汇总.xlsx")
    
    print(f"选择的文件夹: {weekly_folder}")
    print(f"输出文件将保存到: {output_file}")
    
    if not os.path.exists(weekly_folder):
        print(f"错误：找不到文件夹 '{weekly_folder}'")
        return
    
    # 初始化数据容器
    settlement_data = []
    storage_data = []
    all_data = []
    
    # 获取所有CSV文件
    csv_files = [f for f in os.listdir(weekly_folder) if f.endswith('.csv')]
    
    if not csv_files:
        print(f"错误：在 '{weekly_folder}' 文件夹中没有找到CSV文件")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件")
    print("开始处理文件...")
    
    # 处理每个CSV文件
    for idx, filename in enumerate(csv_files, 1):
        filepath = os.path.join(weekly_folder, filename)
        print(f"[{idx}/{len(csv_files)}] 正在处理: {filename}")
        
        # 读取文件
        rows = read_csv_file(filepath)
        if not rows:
            continue
        
        file_data = {
            'filename': filename,
            'rows': rows
        }
        
        # 根据文件名分类
        if 'Settlement' in filename:
            settlement_data.append(file_data)
        elif 'Storage' in filename:
            storage_data.append(file_data)
        
        # 所有数据都添加到总表
        all_data.append(file_data)
    
    print(f"\nSettlement文件: {len(settlement_data)} 个")
    print(f"Storage文件: {len(storage_data)} 个")
    print(f"总文件数: {len(all_data)} 个")
    
    # 创建Excel工作簿
    print(f"\n正在创建Excel文件: {output_file}")
    wb = Workbook()
    
    # 删除默认的工作表
    wb.remove(wb.active)
    
    # 创建Settlement sheet
    ws_settlement = wb.create_sheet("Settlement")
    settlement_rows = write_to_sheet(ws_settlement, settlement_data, "Settlement")
    
    # 创建Storage sheet
    ws_storage = wb.create_sheet("Storage")
    storage_rows = write_to_sheet(ws_storage, storage_data, "Storage")
    
    # 创建总表 sheet
    ws_total = wb.create_sheet("总表")
    total_rows = write_to_sheet(ws_total, all_data, "总表")
    
    # 保存Excel文件
    print(f"\n正在保存Excel文件...")
    wb.save(output_file)
    print(f"Excel文件已保存: {output_file}")
    
    # 显示统计信息
    print("\n=== 统计信息 ===")
    print(f"Settlement sheet: {len(settlement_data)} 个文件")
    print(f"Storage sheet: {len(storage_data)} 个文件")
    print(f"总表 sheet: {len(all_data)} 个文件")
    print(f"文件大小: {os.path.getsize(output_file):,} 字节")
    
    print("\n处理完成！")
    
    # 显示完成消息框
    root = tk.Tk()
    root.withdraw()
    messagebox.showinfo("处理完成", f"数据合并完成！\n\n输出文件：{output_file}\n\n包含 {len(all_data)} 个CSV文件的数据")
    root.destroy()

if __name__ == "__main__":
    try:
        merge_weekly_walmart_to_excel()
    except Exception as e:
        print(f"发生错误：{e}")
        import traceback
        traceback.print_exc()