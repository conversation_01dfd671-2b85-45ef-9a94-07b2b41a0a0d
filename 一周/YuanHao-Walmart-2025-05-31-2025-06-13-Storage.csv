<PERSON>ia<PERSON><PERSON><PERSON>haoshangma<PERSON><PERSON><PERSON><PERSON><PERSON>,,,,,,,,,,,,,,,
View WFS storage fees and how they're calculated at: https://marketplacelearn.walmart.com/guides/Walmart%20Fulfillment%20Services%20(WFS)/WFS%20basics/WFS-fees#Storage,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,
Report start date,Report end date,Total storage fees,Standard storage fees,Peak storage fees,Long-term storage fees,,,,,,,,,,
2025/5/31,2025/6/13,0,0,0,0,,,,,,,,,,
,,,,,,,,,,,,,,,
GTIN,SKU,Item ID,Item Name,Length (in),Width (in),Height (in),Weight (lb),Standard: daily storage fee per unit,Peak: daily storage fee per unit,Long-term: daily storage fee per unit,Standard: average units available,Peak: average units available,Long-term: average units available,Days in report period,Final storage fee
"14-digit Global Trade Item Number, including check digit, that's unique to the item. If it's less than 14 digits, add zeros at the beginning",Unique identifier created by you,Unique identifier assigned by Walmart,Title of the item,"Length of the packaged item, as measured by the fulfillment center","Width of the packaged item, as measured by the fulfillment center","Height of the packaged item, as measured by the fulfillment center","Weight of the packaged item, as measured by the fulfillment center",Daily fee when item is stored fewer than 365 days during January to September,Daily fee when item is stored during the peak months of October to December,Daily fee when item is stored for more than 365 days,Average number of units on hand during this report period that were charged the standard storage fee,Average number of units on hand during this report period that were charged the peak storage fee,Average number of units on hand during this report period that were charged the long-term storage fee,Number of days used to calculate the final storage fee,"To find the final storage fee, multiply each fee by its average units available and the days in report period. Then apply any discounts"
